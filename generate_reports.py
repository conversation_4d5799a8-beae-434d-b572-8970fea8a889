"""
报表生成示例 - 演示如何使用报表生成器
"""

from report_generator import ReportGenerator
import sys


def main():
    """主函数"""
    print("=== 财务报表生成工具 ===\n")
    
    # 创建报表生成器
    generator = ReportGenerator()
    
    # 获取年度参数
    year = 2023
    if len(sys.argv) > 1:
        try:
            year = int(sys.argv[1])
        except ValueError:
            print("年度参数无效，使用默认值2023")
    
    print(f"准备生成 {year} 年度财务报表...")
    
    # 生成所有报表
    reports = generator.generate_all_reports(year)
    
    if reports:
        print("\n🎉 报表生成成功！")
        print("\n📊 生成的报表包括:")
        print("  1. Excel汇总报表 - 包含按科目和按期间的汇总数据")
        print("  2. 余额趋势图 - 显示各期间余额变化趋势")
        print("  3. 科目分布图 - 显示主要科目的余额分布")
        print("  4. 借贷对比图 - 显示各期间借贷发生额对比")
        print("  5. 综合仪表板 - 包含多个图表的综合视图")
        
        print(f"\n📁 所有文件保存在: reports/ 目录下")
        print("\n💡 使用提示:")
        print("  - Excel文件可用于详细数据分析")
        print("  - HTML文件可在浏览器中查看交互式图表")
        print("  - 综合仪表板提供了数据的全面概览")
        
    else:
        print("\n❌ 报表生成失败，请检查:")
        print("  1. 网络连接是否正常")
        print("  2. API配置是否正确")
        print("  3. 指定年度是否有数据")


def quick_demo():
    """快速演示"""
    print("=== 快速演示模式 ===\n")
    
    generator = ReportGenerator()
    
    # 只加载少量数据进行演示
    print("加载少量数据进行演示...")
    if generator.load_data(2023, max_pages=2):
        
        print("\n生成示例图表...")
        
        # 生成一个简单的趋势图
        fig = generator.create_balance_trend_chart()
        if fig:
            print("✅ 余额趋势图生成成功")
        
        # 生成Excel报表
        summary = generator.generate_summary_report()
        if summary:
            print("✅ Excel汇总报表生成成功")
        
        print(f"\n📁 文件保存在: reports/ 目录")
        
    else:
        print("❌ 数据加载失败")


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        quick_demo()
    else:
        main()

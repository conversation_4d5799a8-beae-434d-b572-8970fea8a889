"""
基础版 - Token管理器
"""

import requests
import threading
from datetime import datetime, timedelta
from config import API_CONFIG, TOKEN_CONFIG


class TokenManager:
    """Token管理器"""
    
    def __init__(self):
        self._token = None
        self._token_time = None
        self._lock = threading.Lock()
    
    def get_token(self, force_refresh=False):
        """获取有效的token"""
        with self._lock:
            # 检查现有token是否有效
            if (not force_refresh and self._token and self._token_time and 
                datetime.now() - self._token_time < timedelta(hours=TOKEN_CONFIG['lifetime_hours'])):
                return self._token
            
            # 获取新token
            return self._fetch_new_token()
    
    def _fetch_new_token(self):
        """从API获取新token"""
        params = {
            'from_account': API_CONFIG['from_account'],
            'app_key': API_CONFIG['app_key'],
            'app_secret': API_CONFIG['app_secret']
        }
        
        for attempt in range(TOKEN_CONFIG['retry_attempts']):
            try:
                response = requests.get(
                    API_CONFIG['token_url'], 
                    params=params, 
                    timeout=TOKEN_CONFIG['request_timeout']
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('errcode') == '0':
                        token_data = result.get('token', {})
                        self._token = token_data.get('id')
                        self._token_time = datetime.now()
                        print(f"Token获取成功: {self._token[:20]}...")
                        return self._token
                    else:
                        print(f"Token获取失败: {result.get('errmsg', '未知错误')}")
                else:
                    print(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"请求异常 (第{attempt + 1}次): {str(e)}")
                if attempt < TOKEN_CONFIG['retry_attempts'] - 1:
                    import time
                    time.sleep(1)
        
        print("Token获取失败，已达到最大重试次数")
        return None
    
    def get_token_info(self):
        """获取token信息"""
        if not self._token or not self._token_time:
            return None
        
        remaining = self._token_time + timedelta(hours=TOKEN_CONFIG['lifetime_hours']) - datetime.now()
        return {
            'token': self._token[:20] + '...',
            'obtained_at': self._token_time.isoformat(),
            'remaining_seconds': int(remaining.total_seconds()) if remaining.total_seconds() > 0 else 0,
            'is_expired': remaining.total_seconds() <= 0
        }

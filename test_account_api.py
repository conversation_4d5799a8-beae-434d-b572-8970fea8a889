"""
测试账户汇总API
"""

from account_api import AccountSumAPI
import json


def main():
    """测试账户汇总API功能"""
    
    print("=== 账户汇总API测试 ===\n")
    
    # 创建API实例
    api = AccountSumAPI()
    
    # 1. 测试连接
    print("1. 测试API连接...")
    connection_result = api.test_connection()
    print(f"   连接测试结果: {connection_result['success']}")
    print(f"   消息: {connection_result['message']}")
    print()
    
    if not connection_result['success']:
        print("❌ 连接测试失败，请检查网络和配置")
        return
    
    # 2. 测试单页查询
    print("2. 测试单页查询...")
    try:
        result = api.get_account_sum_batch(2023, 1, 5)  # 查询2023年第1页，每页5条
        
        print(f"   查询结果: {result['success']}")
        if result['success']:
            data = result['data']
            print(f"   响应数据类型: {type(data)}")
            
            if isinstance(data, dict):
                print(f"   响应字段: {list(data.keys())}")
                if 'data' in data:
                    records = data['data']
                    print(f"   记录数: {len(records) if records else 0}")
                    if records and len(records) > 0:
                        print(f"   第一条记录字段: {list(records[0].keys())}")
                        print(f"   第一条记录: {json.dumps(records[0], ensure_ascii=False, indent=2)}")
            else:
                print(f"   数据内容: {data}")
        else:
            print(f"   错误信息: {result['error']}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    print()
    
    # 3. 显示完整响应（用于调试）
    print("3. 完整响应数据:")
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()

"""
基础版 - 账户汇总API
"""

import requests
from datetime import datetime
from token_manager import TokenManager
from config import API_CONFIG, TOKEN_CONFIG


class AccountSumAPI:
    """账户汇总API类"""
    
    def __init__(self):
        self.token_manager = TokenManager()
    
    def get_account_sum(self, year=2023, page=1, rows=20):
        """
        获取账户汇总数据
        
        Args:
            year: 年度
            page: 页码 (从1开始)
            rows: 每页行数
            
        Returns:
            dict: API响应数据
        """
        # 获取token
        token = self.token_manager.get_token()
        if not token:
            return {'success': False, 'error': '无法获取token'}
        
        # 构建请求参数
        params = {
            'from_account': API_CONFIG['from_account'],
            'to_account': API_CONFIG['from_account'],
            'app_key': API_CONFIG['app_key'],
            'token': token,
            'page_index': page,
            'rows_per_page': rows,
            'iyear': year
        }
        
        try:
            # 发送请求
            response = requests.get(
                f"{API_CONFIG['api_base']}/batch_get",
                params=params,
                timeout=TOKEN_CONFIG['request_timeout']
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'data': data,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': f'HTTP错误: {response.status_code}',
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'请求异常: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_all_pages(self, year=2023, max_pages=10, rows=20):
        """
        获取多页数据
        
        Args:
            year: 年度
            max_pages: 最大页数
            rows: 每页行数
            
        Returns:
            dict: 包含所有数据的结果
        """
        all_records = []
        page = 1
        
        while page <= max_pages:
            result = self.get_account_sum(year, page, rows)
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f'第{page}页获取失败: {result["error"]}',
                    'partial_data': all_records,
                    'pages_retrieved': page - 1
                }
            
            # 提取数据
            data = result['data']
            records = data.get('accountsum', [])
            
            if not records:
                break  # 没有更多数据
            
            all_records.extend(records)
            print(f"已获取第{page}页，{len(records)}条记录")
            
            # 如果返回的记录数少于每页行数，说明是最后一页
            if len(records) < rows:
                break
            
            page += 1
        
        return {
            'success': True,
            'data': all_records,
            'total_records': len(all_records),
            'pages_retrieved': page - 1,
            'timestamp': datetime.now().isoformat()
        }
    
    def test_connection(self):
        """测试API连接"""
        try:
            result = self.get_account_sum(datetime.now().year, 1, 1)
            return {
                'success': result['success'],
                'message': '连接测试成功' if result['success'] else f'连接测试失败: {result["error"]}'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'连接测试异常: {str(e)}'
            }

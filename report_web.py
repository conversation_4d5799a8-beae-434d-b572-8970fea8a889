"""
报表Web应用 - 提供Web界面生成和查看报表
"""

from flask import Flask, render_template_string, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS
import os
import json
from datetime import datetime
from report_generator import ReportGenerator
import threading

app = Flask(__name__)
CORS(app)

# 全局报表生成器
report_generator = ReportGenerator()

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务报表生成系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .report-card { margin-bottom: 20px; }
        .status-success { color: #198754; }
        .status-error { color: #dc3545; }
        .status-warning { color: #fd7e14; }
        .loading { display: none; }
        .file-list { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="mb-4">
            <i class="bi bi-graph-up"></i>
            财务报表生成系统
        </h1>

        <!-- 报表生成卡片 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark-bar-graph"></i>
                            生成报表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="report-year" class="form-label">选择年度</label>
                            <input type="number" class="form-control" id="report-year" value="2023" min="2000" max="2100">
                        </div>
                        <div class="mb-3">
                            <label for="max-pages" class="form-label">最大页数</label>
                            <input type="number" class="form-control" id="max-pages" value="5" min="1" max="20">
                        </div>
                        <button id="generate-btn" class="btn btn-primary me-2">
                            <i class="bi bi-play-circle"></i>
                            生成所有报表
                        </button>
                        <button id="quick-demo-btn" class="btn btn-outline-success">
                            <i class="bi bi-lightning"></i>
                            快速演示
                        </button>
                        <div id="generation-status" class="mt-3"></div>
                        <div id="loading" class="loading text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">生成中...</span>
                            </div>
                            <div class="mt-2">正在生成报表，请稍候...</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-folder"></i>
                            报表文件
                        </h5>
                    </div>
                    <div class="card-body">
                        <button id="refresh-files-btn" class="btn btn-outline-primary btn-sm mb-3">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新文件列表
                        </button>
                        <div id="file-list" class="file-list">
                            <div class="text-muted">暂无报表文件</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表预览卡片 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-eye"></i>
                            报表预览
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="report-preview">
                            <div class="text-muted text-center py-4">
                                <i class="bi bi-file-earmark display-4"></i>
                                <div class="mt-2">请先生成报表</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshFileList();
            bindEvents();
        });

        function bindEvents() {
            document.getElementById('generate-btn').addEventListener('click', generateReports);
            document.getElementById('quick-demo-btn').addEventListener('click', quickDemo);
            document.getElementById('refresh-files-btn').addEventListener('click', refreshFileList);
        }

        async function generateReports() {
            const year = document.getElementById('report-year').value;
            const maxPages = document.getElementById('max-pages').value;
            const btn = document.getElementById('generate-btn');
            const loading = document.getElementById('loading');
            const status = document.getElementById('generation-status');

            btn.disabled = true;
            loading.style.display = 'block';
            status.innerHTML = '';

            try {
                const response = await fetch('/api/generate_reports', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ year: parseInt(year), max_pages: parseInt(maxPages) })
                });

                const result = await response.json();

                if (result.success) {
                    status.innerHTML = '<div class="alert alert-success">报表生成成功！</div>';
                    refreshFileList();
                } else {
                    status.innerHTML = `<div class="alert alert-danger">报表生成失败: ${result.error}</div>`;
                }
            } catch (error) {
                status.innerHTML = `<div class="alert alert-danger">请求异常: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        async function quickDemo() {
            const btn = document.getElementById('quick-demo-btn');
            const loading = document.getElementById('loading');
            const status = document.getElementById('generation-status');

            btn.disabled = true;
            loading.style.display = 'block';
            status.innerHTML = '';

            try {
                const response = await fetch('/api/quick_demo', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    status.innerHTML = '<div class="alert alert-success">演示报表生成成功！</div>';
                    refreshFileList();
                } else {
                    status.innerHTML = `<div class="alert alert-danger">演示失败: ${result.error}</div>`;
                }
            } catch (error) {
                status.innerHTML = `<div class="alert alert-danger">请求异常: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        }

        async function refreshFileList() {
            try {
                const response = await fetch('/api/list_files');
                const result = await response.json();

                const fileList = document.getElementById('file-list');
                
                if (result.success && result.files.length > 0) {
                    let html = '<div class="list-group">';
                    result.files.forEach(file => {
                        const icon = file.name.endsWith('.xlsx') ? 'file-earmark-spreadsheet' : 'file-earmark-code';
                        html += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-${icon}"></i>
                                    <span class="ms-2">${file.name}</span>
                                    <small class="text-muted ms-2">(${file.size})</small>
                                </div>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm" onclick="downloadFile('${file.name}')">
                                        <i class="bi bi-download"></i>
                                    </button>
                                    ${file.name.endsWith('.html') ? 
                                        `<button class="btn btn-outline-success btn-sm ms-1" onclick="previewFile('${file.name}')">
                                            <i class="bi bi-eye"></i>
                                        </button>` : ''}
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    fileList.innerHTML = html;
                } else {
                    fileList.innerHTML = '<div class="text-muted">暂无报表文件</div>';
                }
            } catch (error) {
                console.error('刷新文件列表失败:', error);
            }
        }

        function downloadFile(filename) {
            window.open(`/api/download/${filename}`, '_blank');
        }

        function previewFile(filename) {
            window.open(`/api/preview/${filename}`, '_blank');
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/generate_reports', methods=['POST'])
def generate_reports():
    """生成报表API"""
    try:
        data = request.json
        year = data.get('year', 2023)
        max_pages = data.get('max_pages', 5)
        
        # 在后台线程中生成报表
        def generate_in_background():
            report_generator.generate_all_reports(year)
        
        thread = threading.Thread(target=generate_in_background)
        thread.start()
        thread.join(timeout=60)  # 最多等待60秒
        
        return jsonify({'success': True, 'message': '报表生成完成'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/quick_demo', methods=['POST'])
def quick_demo():
    """快速演示API"""
    try:
        # 生成演示报表
        if report_generator.load_data(2023, max_pages=2):
            report_generator.create_balance_trend_chart()
            report_generator.generate_summary_report()
            return jsonify({'success': True, 'message': '演示报表生成完成'})
        else:
            return jsonify({'success': False, 'error': '数据加载失败'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/list_files')
def list_files():
    """列出报表文件"""
    try:
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            return jsonify({'success': True, 'files': []})
        
        files = []
        for filename in os.listdir(reports_dir):
            if filename.endswith(('.xlsx', '.html')):
                filepath = os.path.join(reports_dir, filename)
                size = os.path.getsize(filepath)
                size_str = f"{size/1024:.1f}KB" if size < 1024*1024 else f"{size/(1024*1024):.1f}MB"
                files.append({
                    'name': filename,
                    'size': size_str,
                    'modified': datetime.fromtimestamp(os.path.getmtime(filepath)).strftime('%Y-%m-%d %H:%M')
                })
        
        files.sort(key=lambda x: x['name'], reverse=True)
        return jsonify({'success': True, 'files': files})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        filepath = os.path.join("reports", filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            return "文件不存在", 404
    except Exception as e:
        return f"下载失败: {str(e)}", 500

@app.route('/api/preview/<filename>')
def preview_file(filename):
    """预览HTML文件"""
    try:
        if filename.endswith('.html'):
            filepath = os.path.join("reports", filename)
            if os.path.exists(filepath):
                return send_file(filepath)
        return "文件不存在或不支持预览", 404
    except Exception as e:
        return f"预览失败: {str(e)}", 500

if __name__ == '__main__':
    print("启动财务报表Web应用...")
    print("访问地址: http://localhost:5002")
    app.run(debug=True, host='0.0.0.0', port=5002)

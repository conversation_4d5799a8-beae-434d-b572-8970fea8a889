"""
最简化的用友API对接 - 单文件版本
只需要这一个文件就能对接账户汇总接口
"""

import requests
import json
from datetime import datetime, timedelta
import threading
import time


class MinimalYonyouAPI:
    """最简化的用友API客户端"""
    
    def __init__(self):
        # API配置
        self.token_url = "https://api.yonyouup.com/system/token"
        self.api_base = "https://api.yonyouup.com/api/accountsum"
        self.from_account = "hcopenapi"
        self.app_key = "opad51476ef9c6cffec"
        self.app_secret = "cb3d334a06d946af9f7e73a46c7d3941"
        
        # Token缓存
        self._token = None
        self._token_time = None
        self._lock = threading.Lock()
    
    def get_token(self):
        """获取有效的token"""
        with self._lock:
            # 检查token是否有效（2小时内）
            if (self._token and self._token_time and 
                datetime.now() - self._token_time < timedelta(hours=2)):
                return self._token
            
            # 获取新token
            params = {
                'from_account': self.from_account,
                'app_key': self.app_key,
                'app_secret': self.app_secret
            }
            
            try:
                response = requests.get(self.token_url, params=params, timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('errcode') == '0':
                        token_data = result.get('token', {})
                        self._token = token_data.get('id')
                        self._token_time = datetime.now()
                        print(f"✅ Token获取成功: {self._token[:20]}...")
                        return self._token
                    else:
                        print(f"❌ Token获取失败: {result.get('errmsg', '未知错误')}")
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")
            
            return None
    
    def get_account_sum(self, year=2023, page=1, rows=20):
        """
        获取账户汇总数据
        
        Args:
            year: 年度
            page: 页码
            rows: 每页行数
        
        Returns:
            dict: API响应数据
        """
        token = self.get_token()
        if not token:
            return {"error": "无法获取token"}
        
        params = {
            'from_account': self.from_account,
            'to_account': self.from_account,
            'app_key': self.app_key,
            'token': token,
            'page_index': page,
            'rows_per_page': rows,
            'iyear': year
        }
        
        try:
            response = requests.get(f"{self.api_base}/batch_get", params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 查询成功: 第{page}页，共{data.get('row_count', 0)}条记录")
                return data
            else:
                print(f"❌ 查询失败: HTTP {response.status_code}")
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ 查询异常: {str(e)}")
            return {"error": str(e)}


def main():
    """使用示例"""
    print("=== 用友API最简化对接示例 ===\n")
    
    # 创建API客户端
    api = MinimalYonyouAPI()
    
    # 查询2023年第1页数据，每页5条
    print("正在查询账户汇总数据...")
    result = api.get_account_sum(year=2023, page=1, rows=5)
    
    if 'error' not in result:
        # 显示查询结果
        print(f"\n📊 查询结果:")
        print(f"   总记录数: {result.get('row_count', 0)}")
        print(f"   总页数: {result.get('page_count', 0)}")
        print(f"   当前页: {result.get('page_index', 0)}")
        
        # 显示数据
        records = result.get('accountsum', [])
        if records:
            print(f"\n📋 数据示例 (前{len(records)}条):")
            for i, record in enumerate(records, 1):
                print(f"   {i}. {record.get('ccode', '')} - {record.get('ccode_name', '')} "
                      f"(期末余额: {record.get('me', '0')})")
        
        # 保存完整数据到文件
        with open('account_data.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 完整数据已保存到: account_data.json")
        
    else:
        print(f"\n❌ 查询失败: {result['error']}")


if __name__ == "__main__":
    main()

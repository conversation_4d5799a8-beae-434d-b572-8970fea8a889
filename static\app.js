// 全局变量
let currentData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    bindEvents();
});

// 初始化应用
function initializeApp() {
    loadTokenInfo();
    updateCurrentYear();
}

// 绑定事件
function bindEvents() {
    // Token相关
    document.getElementById('refresh-token-btn').addEventListener('click', refreshToken);
    
    // 连接测试
    document.getElementById('test-connection-btn').addEventListener('click', testConnection);
    
    // 查询按钮
    document.getElementById('query-single-btn').addEventListener('click', querySinglePage);
    document.getElementById('query-all-btn').addEventListener('click', queryAllPages);
    document.getElementById('clear-results-btn').addEventListener('click', clearResults);
    
    // 导出按钮
    document.getElementById('export-csv-btn').addEventListener('click', exportCSV);
    document.getElementById('export-json-btn').addEventListener('click', exportJSON);
}

// 更新当前年度
function updateCurrentYear() {
    const currentYear = new Date().getFullYear();
    document.getElementById('iyear').value = currentYear;
}

// 加载Token信息
async function loadTokenInfo() {
    try {
        const response = await fetch('/api/token_info');
        const result = await response.json();
        
        if (result.success) {
            updateTokenStatus('success', '有效', result.data);
        } else {
            updateTokenStatus('warning', '未获取', null);
        }
    } catch (error) {
        updateTokenStatus('error', '错误', null);
        console.error('加载Token信息失败:', error);
    }
}

// 更新Token状态显示
function updateTokenStatus(type, status, data) {
    const statusElement = document.getElementById('token-status');
    const infoElement = document.getElementById('token-info');
    
    // 清除之前的状态
    statusElement.innerHTML = '';
    infoElement.innerHTML = '';
    
    // 设置新状态
    let badgeClass = 'bg-secondary';
    if (type === 'success') badgeClass = 'bg-success';
    else if (type === 'warning') badgeClass = 'bg-warning';
    else if (type === 'error') badgeClass = 'bg-danger';
    
    statusElement.innerHTML = `<span class="badge ${badgeClass}">${status}</span>`;
    
    if (data) {
        infoElement.innerHTML = `
            <div><strong>Token:</strong> ${data.token}</div>
            <div><strong>获取时间:</strong> ${new Date(data.obtained_at).toLocaleString()}</div>
            <div><strong>过期时间:</strong> ${new Date(data.expires_at).toLocaleString()}</div>
            <div><strong>剩余时间:</strong> ${data.remaining_time}</div>
            <div><strong>请求计数:</strong> ${data.requests_in_period}/${data.max_requests_per_period}</div>
        `;
    }
}

// 刷新Token
async function refreshToken() {
    const btn = document.getElementById('refresh-token-btn');
    const originalText = btn.innerHTML;
    
    try {
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新中...';
        
        const response = await fetch('/api/refresh_token', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            updateTokenStatus('success', '已刷新', result.data);
            showAlert('success', 'Token刷新成功');
        } else {
            updateTokenStatus('error', '刷新失败', null);
            showAlert('danger', `Token刷新失败: ${result.message}`);
        }
    } catch (error) {
        updateTokenStatus('error', '刷新异常', null);
        showAlert('danger', `Token刷新异常: ${error.message}`);
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// 测试连接
async function testConnection() {
    const btn = document.getElementById('test-connection-btn');
    const statusElement = document.getElementById('connection-status');
    const infoElement = document.getElementById('connection-info');
    const originalText = btn.innerHTML;
    
    try {
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-play-circle"></i> 测试中...';
        statusElement.innerHTML = '<span class="badge bg-warning">测试中</span>';
        
        const response = await fetch('/api/test_connection');
        const result = await response.json();
        
        if (result.success) {
            statusElement.innerHTML = '<span class="badge bg-success">连接成功</span>';
            infoElement.innerHTML = `
                <div><strong>测试时间:</strong> ${new Date(result.timestamp).toLocaleString()}</div>
                <div><strong>测试参数:</strong> ${JSON.stringify(result.test_params)}</div>
            `;
            showAlert('success', '连接测试成功');
        } else {
            statusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
            infoElement.innerHTML = `
                <div class="text-danger"><strong>错误:</strong> ${result.message}</div>
                <div><strong>测试时间:</strong> ${new Date(result.timestamp).toLocaleString()}</div>
            `;
            showAlert('danger', `连接测试失败: ${result.message}`);
        }
    } catch (error) {
        statusElement.innerHTML = '<span class="badge bg-danger">连接异常</span>';
        infoElement.innerHTML = `<div class="text-danger"><strong>异常:</strong> ${error.message}</div>`;
        showAlert('danger', `连接测试异常: ${error.message}`);
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

// 查询单页数据
async function querySinglePage() {
    const params = getQueryParams();
    if (!params) return;
    
    showLoading(true);
    
    try {
        const url = `/api/account_sum?iyear=${params.iyear}&page_index=${params.pageIndex}&rows_per_page=${params.rowsPerPage}`;
        const response = await fetch(url);
        const result = await response.json();
        
        displayResults(result, 'single');
        updateRawResponse(result);
        
        if (result.success) {
            showAlert('success', '单页查询成功');
        } else {
            showAlert('danger', `查询失败: ${result.error}`);
        }
    } catch (error) {
        showAlert('danger', `查询异常: ${error.message}`);
        updateRawResponse({ error: error.message });
    } finally {
        showLoading(false);
    }
}

// 查询所有页数据
async function queryAllPages() {
    const params = getQueryParams();
    if (!params) return;
    
    showLoading(true);
    
    try {
        const url = `/api/account_sum/all?iyear=${params.iyear}&max_pages=${params.maxPages}&rows_per_page=${params.rowsPerPage}`;
        const response = await fetch(url);
        const result = await response.json();
        
        displayResults(result, 'all');
        updateRawResponse(result);
        
        if (result.success) {
            showAlert('success', `查询完成，共获取 ${result.total_records} 条记录`);
        } else {
            showAlert('danger', `查询失败: ${result.error}`);
        }
    } catch (error) {
        showAlert('danger', `查询异常: ${error.message}`);
        updateRawResponse({ error: error.message });
    } finally {
        showLoading(false);
    }
}

// 获取查询参数
function getQueryParams() {
    const iyear = parseInt(document.getElementById('iyear').value);
    const pageIndex = parseInt(document.getElementById('page-index').value);
    const rowsPerPage = parseInt(document.getElementById('rows-per-page').value);
    const maxPages = parseInt(document.getElementById('max-pages').value);
    
    // 参数验证
    if (isNaN(iyear) || iyear < 2000 || iyear > 2100) {
        showAlert('warning', '请输入有效的年度 (2000-2100)');
        return null;
    }
    
    if (isNaN(pageIndex) || pageIndex < 1) {
        showAlert('warning', '页码必须大于0');
        return null;
    }
    
    if (isNaN(rowsPerPage) || rowsPerPage < 1 || rowsPerPage > 100) {
        showAlert('warning', '每页行数必须在1-100之间');
        return null;
    }
    
    if (isNaN(maxPages) || maxPages < 1 || maxPages > 50) {
        showAlert('warning', '最大页数必须在1-50之间');
        return null;
    }
    
    return { iyear, pageIndex, rowsPerPage, maxPages };
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingElement = document.getElementById('loading');
    loadingElement.style.display = show ? 'block' : 'none';
}

// 显示查询结果
function displayResults(result, queryType) {
    const infoElement = document.getElementById('query-info');
    const containerElement = document.getElementById('results-container');
    
    // 清空之前的结果
    infoElement.innerHTML = '';
    containerElement.innerHTML = '';
    
    if (result.success) {
        // 显示查询信息
        let info = `<div class="alert alert-info">`;
        info += `<strong>查询时间:</strong> ${new Date(result.timestamp).toLocaleString()}<br>`;
        
        if (queryType === 'single') {
            const data = result.data;
            const records = Array.isArray(data) ? data : (data && data.data ? data.data : []);
            info += `<strong>查询参数:</strong> 年度=${result.request_params.iyear}, 页码=${result.request_params.page_index}, 每页=${result.request_params.rows_per_page}<br>`;
            info += `<strong>返回记录:</strong> ${records.length} 条`;
            currentData = records;
        } else {
            info += `<strong>查询参数:</strong> 年度=${result.request_params.iyear}, 最大页数=${result.request_params.max_pages}, 每页=${result.request_params.rows_per_page}<br>`;
            info += `<strong>总记录数:</strong> ${result.total_records} 条<br>`;
            info += `<strong>获取页数:</strong> ${result.pages_retrieved} 页`;
            currentData = result.data || [];
        }
        
        info += `</div>`;
        infoElement.innerHTML = info;
        
        // 显示数据表格
        if (currentData.length > 0) {
            displayDataTable(currentData);
            enableExportButtons(true);
        } else {
            containerElement.innerHTML = '<div class="alert alert-warning">没有找到数据</div>';
            enableExportButtons(false);
        }
    } else {
        // 显示错误信息
        infoElement.innerHTML = `<div class="alert alert-danger"><strong>查询失败:</strong> ${result.error}</div>`;
        containerElement.innerHTML = '<div class="text-muted text-center py-4">查询失败</div>';
        currentData = [];
        enableExportButtons(false);
    }
}

// 显示数据表格
function displayDataTable(data) {
    if (!data || data.length === 0) return;
    
    const containerElement = document.getElementById('results-container');
    
    // 获取表头
    const headers = Object.keys(data[0]);
    
    let tableHtml = `
        <div class="data-table">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th scope="col">#</th>
    `;
    
    headers.forEach(header => {
        tableHtml += `<th scope="col">${header}</th>`;
    });
    
    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    // 添加数据行
    data.forEach((row, index) => {
        tableHtml += `<tr><td>${index + 1}</td>`;
        headers.forEach(header => {
            const value = row[header] || '';
            tableHtml += `<td>${value}</td>`;
        });
        tableHtml += `</tr>`;
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    containerElement.innerHTML = tableHtml;
}

// 更新原始响应数据显示
function updateRawResponse(data) {
    const element = document.getElementById('raw-response');
    element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
}

// 启用/禁用导出按钮
function enableExportButtons(enable) {
    document.getElementById('export-csv-btn').disabled = !enable;
    document.getElementById('export-json-btn').disabled = !enable;
}

// 清空结果
function clearResults() {
    document.getElementById('query-info').innerHTML = '';
    document.getElementById('results-container').innerHTML = `
        <div class="text-muted text-center py-4">
            <i class="bi bi-inbox display-4"></i>
            <div class="mt-2">暂无查询结果</div>
        </div>
    `;
    document.getElementById('raw-response').innerHTML = '<div class="text-muted">暂无响应数据</div>';
    currentData = [];
    enableExportButtons(false);
}

// 导出CSV
async function exportCSV() {
    if (currentData.length === 0) {
        showAlert('warning', '没有可导出的数据');
        return;
    }
    
    try {
        const response = await fetch('/api/export/csv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ records: currentData })
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `account_sum_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            showAlert('success', 'CSV文件导出成功');
        } else {
            const result = await response.json();
            showAlert('danger', `导出失败: ${result.error}`);
        }
    } catch (error) {
        showAlert('danger', `导出异常: ${error.message}`);
    }
}

// 导出JSON
async function exportJSON() {
    if (currentData.length === 0) {
        showAlert('warning', '没有可导出的数据');
        return;
    }
    
    try {
        const response = await fetch('/api/export/json', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ records: currentData })
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `account_sum_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            showAlert('success', 'JSON文件导出成功');
        } else {
            const result = await response.json();
            showAlert('danger', `导出失败: ${result.error}`);
        }
    } catch (error) {
        showAlert('danger', `导出异常: ${error.message}`);
    }
}

// 显示提示消息
function showAlert(type, message) {
    // 移除之前的提示
    const existingAlert = document.querySelector('.alert-dismissible');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // 创建新提示
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

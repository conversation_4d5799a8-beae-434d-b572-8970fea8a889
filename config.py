"""
配置文件 - 包含API凭据和系统设置
"""

# API配置
API_CONFIG = {
    'base_url': 'https://api.yonyouup.com/system/token',
    'from_account': 'hcopenapi',
    'app_key': 'opad51476ef9c6cffec',
    'app_secret': 'cb3d334a06d946af9f7e73a46c7d3941'
}

# Token管理配置
TOKEN_CONFIG = {
    'lifetime_hours': 2,  # Token生命周期（小时）
    'max_requests_per_period': 2000,  # 2小时内最大请求次数
    'period_hours': 2,  # 限制周期（小时）
    'request_timeout': 30,  # HTTP请求超时时间（秒）
    'retry_attempts': 3,  # 失败重试次数
    'retry_delay': 1  # 重试延迟（秒）
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'filename': 'token_manager.log'
}

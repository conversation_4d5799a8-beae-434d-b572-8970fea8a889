"""
报表生成器 - 基于账户汇总数据生成各种报表和图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import seaborn as sns
from datetime import datetime
import os
from account_api import AccountSumAPI
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class ReportGenerator:
    """报表生成器类"""
    
    def __init__(self):
        self.api = AccountSumAPI()
        self.data = None
        self.df = None
        
        # 创建输出目录
        self.output_dir = "reports"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_data(self, year=2023, max_pages=5):
        """加载账户汇总数据"""
        print(f"正在加载{year}年账户汇总数据...")
        
        result = self.api.get_all_pages(year, max_pages, 50)
        if result['success']:
            self.data = result['data']
            self.df = pd.DataFrame(self.data)
            
            # 数据类型转换
            numeric_columns = ['mb', 'md', 'mc', 'me', 'mb_f', 'md_f', 'mc_f', 'me_f']
            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce').fillna(0)
            
            self.df['iperiod'] = pd.to_numeric(self.df['iperiod'], errors='coerce')
            self.df['iyear'] = pd.to_numeric(self.df['iyear'], errors='coerce')
            
            print(f"数据加载成功: {len(self.data)}条记录")
            return True
        else:
            print(f"数据加载失败: {result['error']}")
            return False
    
    def generate_summary_report(self):
        """生成汇总报表"""
        if self.df is None:
            print("请先加载数据")
            return None
        
        print("生成汇总报表...")
        
        # 按科目汇总
        summary_by_account = self.df.groupby(['ccode', 'ccode_name']).agg({
            'mb': 'first',  # 期初余额
            'me': 'last',   # 期末余额
            'md': 'sum',    # 借方发生额
            'mc': 'sum'     # 贷方发生额
        }).round(2)
        
        # 按期间汇总
        summary_by_period = self.df.groupby('iperiod').agg({
            'me': 'sum',    # 期末余额合计
            'md': 'sum',    # 借方发生额合计
            'mc': 'sum'     # 贷方发生额合计
        }).round(2)
        
        # 保存到Excel
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = f"{self.output_dir}/汇总报表_{timestamp}.xlsx"
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            summary_by_account.to_excel(writer, sheet_name='按科目汇总')
            summary_by_period.to_excel(writer, sheet_name='按期间汇总')
            self.df.to_excel(writer, sheet_name='原始数据', index=False)
        
        print(f"汇总报表已保存: {excel_file}")
        return {
            'excel_file': excel_file,
            'summary_by_account': summary_by_account,
            'summary_by_period': summary_by_period
        }
    
    def create_balance_trend_chart(self):
        """创建余额趋势图"""
        if self.df is None:
            print("请先加载数据")
            return None
        
        print("生成余额趋势图...")
        
        # 按期间汇总余额
        period_balance = self.df.groupby('iperiod')['me'].sum().reset_index()
        
        # 创建Plotly图表
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=period_balance['iperiod'],
            y=period_balance['me'],
            mode='lines+markers',
            name='期末余额',
            line=dict(color='#1f77b4', width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title='账户余额趋势图',
            xaxis_title='期间',
            yaxis_title='余额金额',
            template='plotly_white',
            height=500
        )
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = f"{self.output_dir}/余额趋势图_{timestamp}.html"
        fig.write_html(html_file)
        
        print(f"余额趋势图已保存: {html_file}")
        return fig
    
    def create_account_distribution_chart(self):
        """创建科目分布饼图"""
        if self.df is None:
            print("请先加载数据")
            return None
        
        print("生成科目分布图...")
        
        # 按科目汇总期末余额
        account_balance = self.df.groupby('ccode_name')['me'].sum().reset_index()
        account_balance = account_balance[account_balance['me'] > 0]  # 只显示正余额
        account_balance = account_balance.sort_values('me', ascending=False).head(10)  # 取前10名
        
        # 创建饼图
        fig = px.pie(
            account_balance, 
            values='me', 
            names='ccode_name',
            title='主要科目余额分布 (前10名)'
        )
        
        fig.update_traces(textposition='inside', textinfo='percent+label')
        fig.update_layout(height=600)
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = f"{self.output_dir}/科目分布图_{timestamp}.html"
        fig.write_html(html_file)
        
        print(f"科目分布图已保存: {html_file}")
        return fig
    
    def create_debit_credit_comparison(self):
        """创建借贷发生额对比图"""
        if self.df is None:
            print("请先加载数据")
            return None
        
        print("生成借贷发生额对比图...")
        
        # 按期间汇总借贷发生额
        period_dc = self.df.groupby('iperiod').agg({
            'md': 'sum',  # 借方发生额
            'mc': 'sum'   # 贷方发生额
        }).reset_index()
        
        # 创建对比柱状图
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=period_dc['iperiod'],
            y=period_dc['md'],
            name='借方发生额',
            marker_color='#ff7f0e'
        ))
        
        fig.add_trace(go.Bar(
            x=period_dc['iperiod'],
            y=period_dc['mc'],
            name='贷方发生额',
            marker_color='#2ca02c'
        ))
        
        fig.update_layout(
            title='借贷发生额对比图',
            xaxis_title='期间',
            yaxis_title='发生额',
            barmode='group',
            template='plotly_white',
            height=500
        )
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = f"{self.output_dir}/借贷对比图_{timestamp}.html"
        fig.write_html(html_file)
        
        print(f"借贷发生额对比图已保存: {html_file}")
        return fig
    
    def create_comprehensive_dashboard(self):
        """创建综合仪表板"""
        if self.df is None:
            print("请先加载数据")
            return None
        
        print("生成综合仪表板...")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('余额趋势', '借贷发生额对比', '科目余额分布', '期间统计'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"type": "pie"}, {"type": "table"}]]
        )
        
        # 1. 余额趋势
        period_balance = self.df.groupby('iperiod')['me'].sum().reset_index()
        fig.add_trace(
            go.Scatter(x=period_balance['iperiod'], y=period_balance['me'], 
                      mode='lines+markers', name='期末余额'),
            row=1, col=1
        )
        
        # 2. 借贷发生额对比
        period_dc = self.df.groupby('iperiod').agg({'md': 'sum', 'mc': 'sum'}).reset_index()
        fig.add_trace(
            go.Bar(x=period_dc['iperiod'], y=period_dc['md'], name='借方发生额'),
            row=1, col=2
        )
        fig.add_trace(
            go.Bar(x=period_dc['iperiod'], y=period_dc['mc'], name='贷方发生额'),
            row=1, col=2
        )
        
        # 3. 科目分布饼图
        account_balance = self.df.groupby('ccode_name')['me'].sum().reset_index()
        account_balance = account_balance[account_balance['me'] > 0].head(8)
        fig.add_trace(
            go.Pie(labels=account_balance['ccode_name'], values=account_balance['me'], name="科目分布"),
            row=2, col=1
        )
        
        # 4. 统计表格
        stats_data = [
            ['总记录数', len(self.df)],
            ['科目数量', self.df['ccode'].nunique()],
            ['期间范围', f"{self.df['iperiod'].min()}-{self.df['iperiod'].max()}"],
            ['总余额', f"{self.df['me'].sum():,.2f}"],
            ['借方总额', f"{self.df['md'].sum():,.2f}"],
            ['贷方总额', f"{self.df['mc'].sum():,.2f}"]
        ]
        
        fig.add_trace(
            go.Table(
                header=dict(values=['指标', '数值']),
                cells=dict(values=[[row[0] for row in stats_data], 
                                 [row[1] for row in stats_data]])
            ),
            row=2, col=2
        )
        
        fig.update_layout(height=800, title_text="财务数据综合仪表板")
        
        # 保存仪表板
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = f"{self.output_dir}/综合仪表板_{timestamp}.html"
        fig.write_html(html_file)
        
        print(f"综合仪表板已保存: {html_file}")
        return fig
    
    def generate_all_reports(self, year=2023):
        """生成所有报表"""
        print(f"=== 开始生成{year}年度财务报表 ===\n")
        
        # 1. 加载数据
        if not self.load_data(year, max_pages=10):
            return False
        
        # 2. 生成各种报表
        reports = {}
        
        try:
            # Excel汇总报表
            reports['summary'] = self.generate_summary_report()
            
            # 图表报表
            reports['balance_trend'] = self.create_balance_trend_chart()
            reports['account_distribution'] = self.create_account_distribution_chart()
            reports['debit_credit'] = self.create_debit_credit_comparison()
            reports['dashboard'] = self.create_comprehensive_dashboard()
            
            print(f"\n=== 报表生成完成 ===")
            print(f"输出目录: {os.path.abspath(self.output_dir)}")
            print("生成的文件:")
            for file in os.listdir(self.output_dir):
                if file.endswith(('.xlsx', '.html')):
                    print(f"  - {file}")
            
            return reports
            
        except Exception as e:
            print(f"报表生成失败: {str(e)}")
            return False

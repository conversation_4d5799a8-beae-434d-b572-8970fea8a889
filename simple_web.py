"""
简化版Web应用 - 用于测试基本功能
"""

from flask import Flask, jsonify, render_template_string
from account_api import AccountSumAPI
from token_manager import TokenManager
import json

app = Flask(__name__)

# 创建API实例
account_api = AccountSumAPI()
token_manager = TokenManager()

# 简单的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用友API对接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .success { color: green; }
        .error { color: red; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .form-group { margin: 10px 0; }
        .form-group label { display: inline-block; width: 100px; }
        .form-group input { padding: 5px; border: 1px solid #ddd; border-radius: 4px; }
        #results { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>用友API对接测试界面</h1>
        
        <div class="card">
            <h3>系统状态</h3>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <button class="btn" onclick="getTokenInfo()">获取Token信息</button>
            <button class="btn" onclick="refreshToken()">刷新Token</button>
            <div id="status-results"></div>
        </div>
        
        <div class="card">
            <h3>账户汇总查询</h3>
            <div class="form-group">
                <label>年度:</label>
                <input type="number" id="iyear" value="2023" min="2000" max="2100">
            </div>
            <div class="form-group">
                <label>页码:</label>
                <input type="number" id="page_index" value="1" min="1">
            </div>
            <div class="form-group">
                <label>每页行数:</label>
                <input type="number" id="rows_per_page" value="10" min="1" max="100">
            </div>
            <button class="btn" onclick="queryData()">查询数据</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="card">
            <h3>查询结果</h3>
            <div id="results">暂无查询结果</div>
        </div>
    </div>

    <script>
        async function testConnection() {
            try {
                const response = await fetch('/api/test_connection');
                const result = await response.json();
                document.getElementById('status-results').innerHTML = 
                    `<p class="${result.success ? 'success' : 'error'}">
                        连接测试: ${result.success ? '成功' : '失败'} - ${result.message}
                    </p>`;
            } catch (error) {
                document.getElementById('status-results').innerHTML = 
                    `<p class="error">连接测试异常: ${error.message}</p>`;
            }
        }

        async function getTokenInfo() {
            try {
                const response = await fetch('/api/token_info');
                const result = await response.json();
                if (result.success) {
                    const info = result.data;
                    document.getElementById('status-results').innerHTML = 
                        `<div class="success">
                            <p><strong>Token信息:</strong></p>
                            <p>Token: ${info.token}</p>
                            <p>剩余时间: ${info.remaining_time}</p>
                            <p>请求计数: ${info.requests_in_period}/${info.max_requests_per_period}</p>
                        </div>`;
                } else {
                    document.getElementById('status-results').innerHTML = 
                        `<p class="error">获取Token信息失败: ${result.message}</p>`;
                }
            } catch (error) {
                document.getElementById('status-results').innerHTML = 
                    `<p class="error">获取Token信息异常: ${error.message}</p>`;
            }
        }

        async function refreshToken() {
            try {
                const response = await fetch('/api/refresh_token', { method: 'POST' });
                const result = await response.json();
                document.getElementById('status-results').innerHTML = 
                    `<p class="${result.success ? 'success' : 'error'}">
                        Token刷新: ${result.success ? '成功' : '失败'} - ${result.message}
                    </p>`;
            } catch (error) {
                document.getElementById('status-results').innerHTML = 
                    `<p class="error">Token刷新异常: ${error.message}</p>`;
            }
        }

        async function queryData() {
            const iyear = document.getElementById('iyear').value;
            const page_index = document.getElementById('page_index').value;
            const rows_per_page = document.getElementById('rows_per_page').value;
            
            try {
                const url = `/api/account_sum?iyear=${iyear}&page_index=${page_index}&rows_per_page=${rows_per_page}`;
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    let html = `<div class="success">
                        <p><strong>查询成功!</strong></p>
                        <p>总记录数: ${data.row_count}, 总页数: ${data.page_count}</p>
                        <p>当前页: ${data.page_index}, 每页: ${data.rows_per_page}</p>
                    `;
                    
                    if (data.accountsum && data.accountsum.length > 0) {
                        html += '<table><thead><tr>';
                        const headers = Object.keys(data.accountsum[0]);
                        headers.forEach(header => {
                            html += `<th>${header}</th>`;
                        });
                        html += '</tr></thead><tbody>';
                        
                        data.accountsum.forEach(record => {
                            html += '<tr>';
                            headers.forEach(header => {
                                html += `<td>${record[header] || ''}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody></table>';
                    }
                    html += '</div>';
                    
                    document.getElementById('results').innerHTML = html;
                } else {
                    document.getElementById('results').innerHTML = 
                        `<p class="error">查询失败: ${result.error}</p>`;
                }
            } catch (error) {
                document.getElementById('results').innerHTML = 
                    `<p class="error">查询异常: ${error.message}</p>`;
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '暂无查询结果';
            document.getElementById('status-results').innerHTML = '';
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/test_connection')
def test_connection():
    """测试连接"""
    try:
        result = account_api.test_connection()
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'连接测试异常: {str(e)}'
        })

@app.route('/api/token_info')
def get_token_info():
    """获取token信息"""
    try:
        info = token_manager.get_token_info()
        if info:
            return jsonify({'success': True, 'data': info})
        else:
            return jsonify({'success': False, 'message': '暂无token信息'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取token信息失败: {str(e)}'})

@app.route('/api/refresh_token', methods=['POST'])
def refresh_token():
    """刷新token"""
    try:
        token = token_manager.get_token(force_refresh=True)
        if token:
            return jsonify({
                'success': True,
                'message': 'Token刷新成功',
                'data': token_manager.get_token_info()
            })
        else:
            return jsonify({'success': False, 'message': 'Token刷新失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Token刷新异常: {str(e)}'})

@app.route('/api/account_sum')
def get_account_sum():
    """获取账户汇总数据"""
    try:
        from flask import request
        iyear = request.args.get('iyear', 2023, type=int)
        page_index = request.args.get('page_index', 1, type=int)
        rows_per_page = request.args.get('rows_per_page', 20, type=int)
        
        result = account_api.get_account_sum_batch(iyear, page_index, rows_per_page)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取数据异常: {str(e)}'
        })

if __name__ == '__main__':
    print("启动简化版Web应用...")
    print("访问地址: http://localhost:5001")
    app.run(debug=True, host='0.0.0.0', port=5001)

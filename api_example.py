"""
API客户端使用示例
"""

from api_client import APIClient
import json


def main():
    """演示API客户端的使用"""
    
    print("=== API客户端使用示例 ===\n")
    
    # 创建API客户端
    client = APIClient()
    
    # 1. 查看token信息
    print("1. 当前Token信息:")
    token_info = client.get_token_info()
    if token_info:
        for key, value in token_info.items():
            print(f"   {key}: {value}")
    else:
        print("   暂无token信息")
    
    print("\n" + "="*50)
    
    # 2. 演示如何调用API（这里只是示例，您需要替换为实际的API端点）
    print("2. API调用示例:")
    
    # 示例：调用一个假设的用户信息API
    try:
        print("   准备调用API...")
        
        # 这里是一个示例调用，您需要根据实际的API文档来修改
        # response = client.call_api('GET', '/api/user/info')
        # print(f"   API响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
        
        print("   注意：请根据实际的API端点修改上面的调用代码")
        
    except Exception as e:
        print(f"   API调用失败: {str(e)}")
    
    print("\n" + "="*50)
    
    # 3. 演示token刷新
    print("3. Token刷新示例:")
    try:
        success = client.refresh_token()
        if success:
            print("   Token刷新成功")
            
            # 显示新的token信息
            new_token_info = client.get_token_info()
            if new_token_info:
                print("   新Token信息:")
                for key, value in new_token_info.items():
                    print(f"     {key}: {value}")
        else:
            print("   Token刷新失败")
    except Exception as e:
        print(f"   Token刷新异常: {str(e)}")
    
    print("\n=== 示例完成 ===")


def api_call_template():
    """
    API调用模板函数
    您可以复制这个函数并根据实际需求修改
    """
    client = APIClient()
    
    try:
        # GET请求示例
        response = client.call_api(
            method='GET',
            endpoint='/your/api/endpoint',
            params={'param1': 'value1', 'param2': 'value2'}
        )
        print("GET请求成功:", response)
        
        # POST请求示例
        response = client.call_api(
            method='POST',
            endpoint='/your/api/endpoint',
            json_data={'key1': 'value1', 'key2': 'value2'}
        )
        print("POST请求成功:", response)
        
        # PUT请求示例
        response = client.call_api(
            method='PUT',
            endpoint='/your/api/endpoint/123',
            json_data={'key1': 'updated_value1'}
        )
        print("PUT请求成功:", response)
        
        # DELETE请求示例
        response = client.call_api(
            method='DELETE',
            endpoint='/your/api/endpoint/123'
        )
        print("DELETE请求成功:", response)
        
    except Exception as e:
        print(f"API调用失败: {str(e)}")


if __name__ == "__main__":
    main()
    
    print("\n" + "="*60)
    print("API调用模板（请根据实际需求修改）:")
    print("="*60)
    
    # 显示模板代码
    template_code = '''
from api_client import APIClient

# 创建客户端
client = APIClient()

# 调用API
try:
    response = client.call_api(
        method='GET',  # 或 POST, PUT, DELETE
        endpoint='/your/api/endpoint',
        params={'param1': 'value1'},  # GET请求参数
        json_data={'key': 'value'}    # POST/PUT请求数据
    )
    print("API调用成功:", response)
except Exception as e:
    print(f"API调用失败: {e}")
'''
    
    print(template_code)

# 用友API对接与财务报表生成系统

## 项目概述

本项目实现了用友API的完整对接解决方案，包括自动化Token管理、账户汇总数据获取、以及专业的财务报表生成与可视化功能。

## 核心功能

### 🔐 自动化Token管理
- 自动获取和缓存用友API访问Token
- 智能过期检查和自动刷新（2小时生命周期）
- 频率限制控制（2小时内最多2000次请求）
- 线程安全的并发访问支持

### 📊 API数据对接
- **已对接接口**: 账户汇总批量获取接口
- **接口地址**: `https://api.yonyouup.com/api/accountsum/batch_get`
- **支持功能**: 分页查询、批量数据获取、参数验证
- **数据处理**: 自动解析JSON响应、数据格式化

### 📈 财务报表生成
- **Excel汇总报表**: 按科目和期间的详细汇总分析
- **交互式图表**: 余额趋势、科目分布、借贷对比等可视化图表
- **综合仪表板**: 多维度数据展示的综合视图
- **多种格式**: 支持Excel(.xlsx)和HTML(.html)格式导出

### 🌐 Web管理界面
- 直观的Web操作界面
- 实时数据查询和展示
- 在线报表生成和下载
- 系统状态监控

## 技术架构

### 后端技术
- **Python 3.13**: 核心开发语言
- **Flask**: Web框架
- **Requests**: HTTP客户端
- **Pandas**: 数据处理和分析
- **Plotly**: 交互式图表生成
- **Matplotlib**: 基础图表绘制
- **OpenPyXL**: Excel文件操作

### 核心模块
- `token_manager.py`: Token自动管理
- `account_api.py`: 账户汇总API封装
- `report_generator.py`: 报表生成引擎
- `report_web.py`: Web管理界面
- `config.py`: 系统配置管理
- `utils.py`: 通用工具函数

## 项目结构

```
用友API对接系统/
├── config.py              # API配置和系统设置
├── token_manager.py       # Token自动管理模块
├── account_api.py         # 账户汇总API接口封装
├── api_client.py          # 通用API客户端
├── utils.py               # 工具函数库
├── report_generator.py    # 报表生成引擎
├── report_web.py          # Web管理界面
├── generate_reports.py    # 命令行报表生成工具
├── requirements.txt       # Python依赖包列表
├── reports/               # 报表输出目录
│   ├── *.xlsx            # Excel格式报表
│   └── *.html            # 交互式图表
└── README.md             # 项目文档（本文件）
```

## 安装与配置

### 1. 环境要求
- Python 3.8 或更高版本
- Windows/Linux/macOS 操作系统

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置说明
系统配置位于 `config.py` 文件中，包含以下关键配置：

```python
# API配置（已预配置，无需修改）
API_CONFIG = {
    'base_url': 'https://api.yonyouup.com/system/token',
    'from_account': 'hcopenapi',
    'app_key': 'opad51476ef9c6cffec',
    'app_secret': 'cb3d334a06d946af9f7e73a46c7d3941'
}

# Token管理配置
TOKEN_CONFIG = {
    'lifetime_hours': 2,           # Token生命周期
    'max_requests_per_period': 2000,  # 最大请求频率
    'request_timeout': 30,         # 请求超时时间
    'retry_attempts': 3            # 失败重试次数
}
```

## 使用指南

### 方式一：Web界面（推荐）

#### 1. 启动Web应用
```bash
python report_web.py
```

启动成功后，控制台会显示：
```
启动财务报表Web应用...
访问地址: http://localhost:5002
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:5002
* Running on http://192.168.x.x:5002
```

#### 2. 访问系统
打开浏览器访问：`http://localhost:5002`

#### 3. 功能操作
- **生成报表**: 选择年度和页数，点击"生成所有报表"
- **快速演示**: 点击"快速演示"生成示例报表
- **查看文件**: 在"报表文件"区域查看和下载生成的报表
- **在线预览**: 点击HTML文件的预览按钮在线查看图表

### 方式二：命令行工具

#### 1. 生成完整报表
```bash
# 生成指定年度的完整报表
python generate_reports.py 2023

# 使用默认年度（2023）
python generate_reports.py
```

#### 2. 快速演示
```bash
python generate_reports.py demo
```

### 方式三：编程接口

```python
from report_generator import ReportGenerator

# 创建报表生成器
generator = ReportGenerator()

# 生成所有报表
reports = generator.generate_all_reports(year=2023)

# 生成单个报表
generator.load_data(2023, max_pages=5)
generator.create_balance_trend_chart()
generator.generate_summary_report()
```

## 报表说明

### 1. Excel汇总报表
- **文件格式**: .xlsx
- **包含工作表**:
  - 按科目汇总：每个会计科目的期初、期末余额和发生额
  - 按期间汇总：每个会计期间的总余额和发生额
  - 原始数据：完整的API响应数据

### 2. 交互式图表
- **余额趋势图**: 显示各期间账户余额的变化趋势
- **科目分布图**: 饼图展示主要科目的余额分布情况
- **借贷对比图**: 柱状图对比各期间的借贷发生额
- **综合仪表板**: 集成多个图表的综合分析视图

### 3. 图表特点
- **交互性**: 支持缩放、悬停显示详情、图例切换
- **响应式**: 自适应不同屏幕尺寸
- **专业性**: 采用专业的财务图表样式和配色

## API接口详情

### 已对接接口：账户汇总批量获取

**接口信息**:
- **URL**: `https://api.yonyouup.com/api/accountsum/batch_get`
- **方法**: GET
- **功能**: 获取指定年度的账户汇总数据

**请求参数**:
- `from_account`: 源账户（固定值）
- `to_account`: 目标账户（固定值）
- `app_key`: 应用密钥（固定值）
- `token`: 访问令牌（自动获取）
- `page_index`: 页码（从1开始）
- `rows_per_page`: 每页行数（1-100）
- `iyear`: 查询年度（如2023）

**响应数据结构**:
```json
{
  "page_count": "766",      // 总页数
  "rows_per_page": "20",    // 每页行数
  "row_count": "3828",      // 总记录数
  "page_index": "1",        // 当前页码
  "errcode": "0",           // 错误代码
  "errmsg": "",             // 错误消息
  "accountsum": [           // 账户汇总数据
    {
      "i_id": "24773",           // 记录ID
      "ccode": "1001",           // 科目代码
      "ccode_name": "库存现金",   // 科目名称
      "iperiod": "1",            // 会计期间
      "cbegind_c": "借",         // 期初方向
      "mb": "531.3800",          // 期初余额
      "md": "0.0000",            // 借方发生额
      "mc": "39.0000",           // 贷方发生额
      "cendd_c": "借",           // 期末方向
      "me": "492.3800",          // 期末余额
      "iyear": "2023"            // 年度
    }
  ]
}
```

## 系统监控

### Token状态监控
- 实时显示Token有效期和剩余时间
- 自动监控请求频率和限制状态
- 支持手动刷新Token

### 连接状态检测
- 自动测试API连接状态
- 网络异常自动重试
- 详细的错误日志记录

### 数据质量保证
- 自动数据类型转换和验证
- 异常数据处理和过滤
- 完整的操作日志记录

## 故障排除

### 常见问题

1. **Token获取失败**
   - 检查网络连接
   - 验证API配置信息
   - 查看 `token_manager.log` 日志文件

2. **数据查询失败**
   - 确认指定年度是否有数据
   - 检查请求参数是否正确
   - 验证Token是否有效

3. **报表生成失败**
   - 确保已安装所有依赖包
   - 检查 `reports/` 目录权限
   - 查看控制台错误信息

4. **Web界面无法访问**
   - 确认端口5002未被占用
   - 检查防火墙设置
   - 验证Flask应用是否正常启动

### 日志文件
- `token_manager.log`: Token管理操作日志
- 控制台输出：实时操作状态和错误信息

## 扩展开发

### 添加新的API接口
基于现有架构，可以轻松扩展更多用友API接口：

```python
# 在 account_api.py 中添加新接口
def get_voucher_data(self, year, month):
    """获取凭证数据"""
    # 实现新接口逻辑
    pass
```

### 自定义报表类型
```python
# 在 report_generator.py 中添加新报表
def create_custom_report(self):
    """创建自定义报表"""
    # 实现报表生成逻辑
    pass
```

### 定时任务
```python
import schedule

def auto_generate_reports():
    """自动生成报表"""
    generator = ReportGenerator()
    generator.generate_all_reports()

# 每天上午9点自动生成报表
schedule.every().day.at("09:00").do(auto_generate_reports)
```

## 技术支持

如需技术支持或功能扩展，请联系开发团队。

---

**项目版本**: 1.0.0
**最后更新**: 2025-07-22
**开发状态**: 生产就绪 ✅

# Token管理器

这是一个用于管理用友API token的Python工具，能够自动获取、缓存和管理token，确保高效且合规的API访问。

## 功能特性

- **自动token管理**: 自动获取和缓存token，避免重复请求
- **生命周期管理**: 自动检查token过期时间（2小时）
- **频率限制**: 确保2小时内token请求次数不超过2000次
- **线程安全**: 支持多线程环境下的安全使用
- **错误处理**: 完善的异常处理和重试机制
- **日志记录**: 详细的操作日志，便于调试和监控

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 基本使用

```python
from token_manager import TokenManager

# 创建token管理器
token_manager = TokenManager()

# 获取token
token = token_manager.get_token()
if token:
    print(f"获取到token: {token}")
    
    # 使用token进行API调用
    # headers = {'Authorization': f'Bearer {token}'}
    # response = requests.get('your_api_endpoint', headers=headers)
else:
    print("获取token失败")
```

### 查看token信息

```python
# 获取token详细信息
token_info = token_manager.get_token_info()
if token_info:
    print("Token信息:")
    for key, value in token_info.items():
        print(f"  {key}: {value}")
```

### 强制刷新token

```python
# 强制获取新token（忽略缓存）
new_token = token_manager.get_token(force_refresh=True)
```

## 配置说明

### API配置 (config.py)

- `base_url`: Token获取API地址
- `from_account`: 账户标识
- `app_key`: 应用密钥
- `app_secret`: 应用秘钥

### Token配置

- `lifetime_hours`: Token生命周期（默认2小时）
- `max_requests_per_period`: 周期内最大请求次数（默认2000次）
- `period_hours`: 限制周期（默认2小时）
- `request_timeout`: HTTP请求超时时间（默认30秒）
- `retry_attempts`: 失败重试次数（默认3次）

## 文件结构

```
├── token_manager.py    # 主要的token管理类
├── api_client.py      # API客户端（推荐使用）
├── config.py          # 配置文件
├── utils.py           # 工具函数
├── main.py            # TokenManager使用示例
├── api_example.py     # APIClient使用示例
├── requirements.txt   # 依赖包列表
└── README.md          # 说明文档
```

## 运行示例

```bash
python main.py
```

## 注意事项

1. **频率限制**: 系统会自动跟踪请求频率，确保不超过限制
2. **Token缓存**: 有效的token会被缓存，避免不必要的API请求
3. **线程安全**: 可以在多线程环境中安全使用
4. **日志文件**: 操作日志会保存到 `token_manager.log` 文件中

## API调用示例

### 方法一：使用APIClient（推荐）

```python
from api_client import APIClient

# 创建API客户端（自动管理token）
client = APIClient()

# GET请求
try:
    response = client.call_api(
        method='GET',
        endpoint='/your/api/endpoint',
        params={'param1': 'value1'}
    )
    print("API调用成功:", response)
except Exception as e:
    print(f"API调用失败: {e}")

# POST请求
try:
    response = client.call_api(
        method='POST',
        endpoint='/your/api/endpoint',
        json_data={'key': 'value'}
    )
    print("API调用成功:", response)
except Exception as e:
    print(f"API调用失败: {e}")
```

### 方法二：直接使用TokenManager

```python
import requests
from token_manager import TokenManager

# 获取token
token_manager = TokenManager()
token = token_manager.get_token()

if token:
    # 使用token调用API
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    response = requests.get(
        'https://api.yonyouup.com/your_api_endpoint',
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        print("API调用成功:", data)
    else:
        print("API调用失败:", response.status_code)
```

## 故障排除

1. **网络连接问题**: 检查网络连接和防火墙设置
2. **认证失败**: 验证config.py中的API凭据是否正确
3. **频率限制**: 如果遇到频率限制，等待一段时间后重试
4. **日志查看**: 查看 `token_manager.log` 文件获取详细错误信息

"""
Token管理器使用示例
"""

import time
from token_manager import TokenManager


def main():
    """主函数 - 演示token管理器的使用"""
    
    print("=== Token管理器使用示例 ===\n")
    
    # 创建token管理器实例
    token_manager = TokenManager()
    
    # 1. 获取token
    print("1. 获取token:")
    token = token_manager.get_token()
    if token:
        print(f"   成功获取token: {token[:30]}...")
    else:
        print("   获取token失败")
        return
    
    # 2. 查看token信息
    print("\n2. Token信息:")
    token_info = token_manager.get_token_info()
    if token_info:
        for key, value in token_info.items():
            print(f"   {key}: {value}")
    
    # 3. 再次获取token（应该使用缓存）
    print("\n3. 再次获取token（使用缓存）:")
    token2 = token_manager.get_token()
    if token2:
        print(f"   获取到token: {token2[:30]}...")
        print(f"   是否为同一个token: {token == token2}")
    
    # 4. 强制刷新token
    print("\n4. 强制刷新token:")
    token3 = token_manager.get_token(force_refresh=True)
    if token3:
        print(f"   新token: {token3[:30]}...")
        print(f"   是否为新token: {token != token3}")
    
    # 5. 查看更新后的token信息
    print("\n5. 更新后的Token信息:")
    token_info = token_manager.get_token_info()
    if token_info:
        for key, value in token_info.items():
            print(f"   {key}: {value}")
    
    print("\n=== 示例完成 ===")


def api_call_example(token_manager: TokenManager):
    """
    模拟API调用的示例函数
    
    Args:
        token_manager: Token管理器实例
    """
    print("\n=== API调用示例 ===")
    
    # 获取token用于API调用
    token = token_manager.get_token()
    if not token:
        print("无法获取token，API调用失败")
        return
    
    print(f"使用token进行API调用: {token[:20]}...")
    
    # 这里可以添加实际的API调用代码
    # 例如：
    # headers = {'Authorization': f'Bearer {token}'}
    # response = requests.get('your_api_endpoint', headers=headers)
    
    print("API调用完成")


if __name__ == "__main__":
    main()
    
    # 可选：演示API调用
    print("\n" + "="*50)
    token_manager = TokenManager()
    api_call_example(token_manager)

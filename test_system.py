"""
系统测试脚本 - 验证token管理系统的各项功能
"""

import time
from token_manager import TokenManager
from api_client import APIClient


def test_token_manager():
    """测试TokenManager功能"""
    print("=== 测试TokenManager ===")
    
    manager = TokenManager()
    
    # 测试1：获取token
    print("1. 测试获取token...")
    token1 = manager.get_token()
    assert token1 is not None, "获取token失败"
    print(f"   ✓ 成功获取token: {token1[:20]}...")
    
    # 测试2：缓存功能
    print("2. 测试token缓存...")
    token2 = manager.get_token()
    assert token1 == token2, "缓存功能失败"
    print("   ✓ token缓存正常工作")
    
    # 测试3：token信息
    print("3. 测试token信息...")
    info = manager.get_token_info()
    assert info is not None, "获取token信息失败"
    assert not info['is_expired'], "token不应该过期"
    print("   ✓ token信息获取正常")
    
    # 测试4：强制刷新
    print("4. 测试强制刷新...")
    token3 = manager.get_token(force_refresh=True)
    assert token3 is not None, "强制刷新失败"
    assert token3 != token1, "强制刷新应该获取新token"
    print(f"   ✓ 强制刷新成功: {token3[:20]}...")
    
    print("TokenManager测试完成！\n")
    return True


def test_api_client():
    """测试APIClient功能"""
    print("=== 测试APIClient ===")
    
    client = APIClient()
    
    # 测试1：客户端初始化
    print("1. 测试客户端初始化...")
    assert client.base_url == "https://api.yonyouup.com", "基础URL设置错误"
    print("   ✓ 客户端初始化成功")
    
    # 测试2：token信息获取
    print("2. 测试token信息获取...")
    info = client.get_token_info()
    # 第一次调用可能没有token，这是正常的
    print("   ✓ token信息获取功能正常")
    
    # 测试3：token刷新
    print("3. 测试token刷新...")
    success = client.refresh_token()
    assert success, "token刷新失败"
    print("   ✓ token刷新成功")
    
    # 测试4：验证刷新后的token信息
    print("4. 验证刷新后的token信息...")
    info = client.get_token_info()
    assert info is not None, "刷新后应该有token信息"
    assert not info['is_expired'], "刷新后的token不应该过期"
    print("   ✓ 刷新后的token信息正常")
    
    print("APIClient测试完成！\n")
    return True


def test_frequency_limit():
    """测试频率限制功能"""
    print("=== 测试频率限制 ===")
    
    manager = TokenManager()
    
    # 清除历史记录，重新开始
    manager._request_history = []
    
    print("1. 测试正常请求...")
    token = manager.get_token(force_refresh=True)
    assert token is not None, "正常请求应该成功"
    print("   ✓ 正常请求成功")
    
    print("2. 检查请求计数...")
    info = manager.get_token_info()
    assert info['requests_in_period'] > 0, "请求计数应该大于0"
    print(f"   ✓ 当前请求计数: {info['requests_in_period']}")
    
    print("频率限制测试完成！\n")
    return True


def test_error_handling():
    """测试错误处理"""
    print("=== 测试错误处理 ===")
    
    # 这里可以添加更多的错误处理测试
    # 比如网络错误、无效响应等
    
    print("1. 测试基本错误处理...")
    try:
        client = APIClient("https://invalid-url-that-does-not-exist.com")
        # 这个调用应该会失败，但不应该崩溃程序
        print("   ✓ 错误处理机制正常")
    except Exception as e:
        print(f"   ✓ 捕获到预期的异常: {type(e).__name__}")
    
    print("错误处理测试完成！\n")
    return True


def main():
    """运行所有测试"""
    print("开始系统测试...\n")
    
    tests = [
        ("TokenManager功能", test_token_manager),
        ("APIClient功能", test_api_client),
        ("频率限制", test_frequency_limit),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"运行测试: {test_name}")
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过\n")
            else:
                print(f"✗ {test_name} 测试失败\n")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {str(e)}\n")
    
    print("="*50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

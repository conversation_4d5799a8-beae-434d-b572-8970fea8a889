<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用友API对接 - 账户汇总</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-success { color: #198754; }
        .status-error { color: #dc3545; }
        .status-warning { color: #fd7e14; }
        .card-header { background-color: #f8f9fa; }
        .data-table { max-height: 400px; overflow-y: auto; }
        .loading { display: none; }
        .token-info { font-family: monospace; font-size: 0.9em; }
        .json-viewer { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-cloud-arrow-down"></i>
                    用友API对接 - 账户汇总
                </h1>
            </div>
        </div>

        <!-- Token状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-key"></i>
                            Token状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="token-status" class="mb-3">
                            <span class="badge bg-secondary">未知</span>
                        </div>
                        <div id="token-info" class="token-info small text-muted"></div>
                        <button id="refresh-token-btn" class="btn btn-outline-primary btn-sm mt-2">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新Token
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-wifi"></i>
                            连接测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="connection-status" class="mb-3">
                            <span class="badge bg-secondary">未测试</span>
                        </div>
                        <div id="connection-info" class="small text-muted"></div>
                        <button id="test-connection-btn" class="btn btn-outline-success btn-sm mt-2">
                            <i class="bi bi-play-circle"></i>
                            测试连接
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查询参数卡片 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-search"></i>
                            查询参数
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="iyear" class="form-label">年度</label>
                                <input type="number" class="form-control" id="iyear" value="2023" min="2000" max="2100">
                            </div>
                            <div class="col-md-3">
                                <label for="page-index" class="form-label">页码</label>
                                <input type="number" class="form-control" id="page-index" value="1" min="1">
                            </div>
                            <div class="col-md-3">
                                <label for="rows-per-page" class="form-label">每页行数</label>
                                <input type="number" class="form-control" id="rows-per-page" value="20" min="1" max="100">
                            </div>
                            <div class="col-md-3">
                                <label for="max-pages" class="form-label">最大页数</label>
                                <input type="number" class="form-control" id="max-pages" value="10" min="1" max="50">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button id="query-single-btn" class="btn btn-primary me-2">
                                    <i class="bi bi-search"></i>
                                    查询单页
                                </button>
                                <button id="query-all-btn" class="btn btn-success me-2">
                                    <i class="bi bi-collection"></i>
                                    查询所有页
                                </button>
                                <button id="clear-results-btn" class="btn btn-outline-secondary">
                                    <i class="bi bi-trash"></i>
                                    清空结果
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查询结果卡片 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-table"></i>
                            查询结果
                        </h5>
                        <div>
                            <button id="export-csv-btn" class="btn btn-outline-primary btn-sm me-2" disabled>
                                <i class="bi bi-file-earmark-spreadsheet"></i>
                                导出CSV
                            </button>
                            <button id="export-json-btn" class="btn btn-outline-info btn-sm" disabled>
                                <i class="bi bi-file-earmark-code"></i>
                                导出JSON
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="loading" class="loading text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在查询数据...</div>
                        </div>
                        <div id="query-info" class="mb-3"></div>
                        <div id="results-container">
                            <div class="text-muted text-center py-4">
                                <i class="bi bi-inbox display-4"></i>
                                <div class="mt-2">暂无查询结果</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 原始响应数据卡片 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-code-square"></i>
                            原始响应数据
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="raw-response" class="json-viewer">
                            <div class="text-muted">暂无响应数据</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>

"""
API客户端 - 封装了token管理和API调用的完整客户端
"""

import requests
from typing import Optional, Dict, Any, Union
from token_manager import TokenManager
from utils import setup_logger


class APIClient:
    """
    API客户端类
    
    功能：
    - 自动管理token
    - 封装API调用
    - 统一错误处理
    - 自动重试机制
    """
    
    def __init__(self, base_url: str = "https://api.yonyouup.com"):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.token_manager = TokenManager()
        self.logger = setup_logger('APIClient')
        
        self.logger.info(f"API客户端初始化完成，基础URL: {self.base_url}")
    
    def _get_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        获取请求头，包含认证token
        
        Args:
            additional_headers: 额外的请求头
            
        Returns:
            完整的请求头字典
        """
        token = self.token_manager.get_token()
        if not token:
            raise Exception("无法获取有效的token")
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, 
            headers: Optional[Dict[str, str]] = None, timeout: int = 30) -> requests.Response:
        """
        发送GET请求
        
        Args:
            endpoint: API端点（相对路径）
            params: 查询参数
            headers: 额外的请求头
            timeout: 请求超时时间
            
        Returns:
            响应对象
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        request_headers = self._get_headers(headers)
        
        self.logger.info(f"发送GET请求: {url}")
        
        response = requests.get(
            url,
            params=params,
            headers=request_headers,
            timeout=timeout
        )
        
        self.logger.info(f"GET请求完成，状态码: {response.status_code}")
        return response
    
    def post(self, endpoint: str, data: Optional[Union[Dict[str, Any], str]] = None,
             json_data: Optional[Dict[str, Any]] = None,
             headers: Optional[Dict[str, str]] = None, timeout: int = 30) -> requests.Response:
        """
        发送POST请求
        
        Args:
            endpoint: API端点（相对路径）
            data: 表单数据
            json_data: JSON数据
            headers: 额外的请求头
            timeout: 请求超时时间
            
        Returns:
            响应对象
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        request_headers = self._get_headers(headers)
        
        self.logger.info(f"发送POST请求: {url}")
        
        response = requests.post(
            url,
            data=data,
            json=json_data,
            headers=request_headers,
            timeout=timeout
        )
        
        self.logger.info(f"POST请求完成，状态码: {response.status_code}")
        return response
    
    def put(self, endpoint: str, data: Optional[Union[Dict[str, Any], str]] = None,
            json_data: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None, timeout: int = 30) -> requests.Response:
        """
        发送PUT请求
        
        Args:
            endpoint: API端点（相对路径）
            data: 表单数据
            json_data: JSON数据
            headers: 额外的请求头
            timeout: 请求超时时间
            
        Returns:
            响应对象
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        request_headers = self._get_headers(headers)
        
        self.logger.info(f"发送PUT请求: {url}")
        
        response = requests.put(
            url,
            data=data,
            json=json_data,
            headers=request_headers,
            timeout=timeout
        )
        
        self.logger.info(f"PUT请求完成，状态码: {response.status_code}")
        return response
    
    def delete(self, endpoint: str, params: Optional[Dict[str, Any]] = None,
               headers: Optional[Dict[str, str]] = None, timeout: int = 30) -> requests.Response:
        """
        发送DELETE请求
        
        Args:
            endpoint: API端点（相对路径）
            params: 查询参数
            headers: 额外的请求头
            timeout: 请求超时时间
            
        Returns:
            响应对象
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        request_headers = self._get_headers(headers)
        
        self.logger.info(f"发送DELETE请求: {url}")
        
        response = requests.delete(
            url,
            params=params,
            headers=request_headers,
            timeout=timeout
        )
        
        self.logger.info(f"DELETE请求完成，状态码: {response.status_code}")
        return response
    
    def call_api(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        通用API调用方法，自动处理响应和错误
        
        Args:
            method: HTTP方法（GET, POST, PUT, DELETE）
            endpoint: API端点
            **kwargs: 其他参数
            
        Returns:
            解析后的JSON响应
            
        Raises:
            Exception: API调用失败时抛出异常
        """
        method = method.upper()
        
        try:
            if method == 'GET':
                response = self.get(endpoint, **kwargs)
            elif method == 'POST':
                response = self.post(endpoint, **kwargs)
            elif method == 'PUT':
                response = self.put(endpoint, **kwargs)
            elif method == 'DELETE':
                response = self.delete(endpoint, **kwargs)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 检查响应状态
            if response.status_code == 200:
                try:
                    return response.json()
                except ValueError:
                    # 如果不是JSON响应，返回文本内容
                    return {'content': response.text}
            else:
                error_msg = f"API调用失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                self.logger.error(f"响应内容: {response.text}")
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            error_msg = f"请求异常: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def get_token_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前token信息
        
        Returns:
            token信息字典
        """
        return self.token_manager.get_token_info()
    
    def refresh_token(self) -> bool:
        """
        强制刷新token
        
        Returns:
            刷新是否成功
        """
        token = self.token_manager.get_token(force_refresh=True)
        return token is not None

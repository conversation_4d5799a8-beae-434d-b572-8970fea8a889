"""
工具函数模块
"""

import logging
from datetime import datetime, timedelta
from typing import Optional
from config import LOG_CONFIG


def setup_logger(name: str = 'TokenManager') -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        # 设置日志级别
        logger.setLevel(getattr(logging, LOG_CONFIG['level']))
        
        # 创建文件处理器
        file_handler = logging.FileHandler(
            LOG_CONFIG['filename'], 
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, LOG_CONFIG['level']))
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, LOG_CONFIG['level']))
        
        # 创建格式化器
        formatter = logging.Formatter(LOG_CONFIG['format'])
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器到日志记录器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger


def is_token_expired(token_time: datetime, lifetime_hours: int = 2) -> bool:
    """
    检查token是否过期
    
    Args:
        token_time: token获取时间
        lifetime_hours: token生命周期（小时）
        
    Returns:
        True如果过期，False如果仍有效
    """
    expiry_time = token_time + timedelta(hours=lifetime_hours)
    return datetime.now() >= expiry_time


def get_remaining_time(token_time: datetime, lifetime_hours: int = 2) -> Optional[timedelta]:
    """
    获取token剩余有效时间
    
    Args:
        token_time: token获取时间
        lifetime_hours: token生命周期（小时）
        
    Returns:
        剩余时间，如果已过期则返回None
    """
    expiry_time = token_time + timedelta(hours=lifetime_hours)
    remaining = expiry_time - datetime.now()
    
    if remaining.total_seconds() > 0:
        return remaining
    return None


def format_time_remaining(remaining_time: timedelta) -> str:
    """
    格式化剩余时间显示
    
    Args:
        remaining_time: 剩余时间
        
    Returns:
        格式化的时间字符串
    """
    total_seconds = int(remaining_time.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

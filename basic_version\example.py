"""
基础版 - 使用示例
"""

from account_api import AccountSumAPI
import json


def main():
    """使用示例"""
    print("=== 用友API基础版使用示例 ===\n")
    
    # 创建API实例
    api = AccountSumAPI()
    
    # 1. 测试连接
    print("1. 测试连接...")
    conn_result = api.test_connection()
    print(f"   结果: {conn_result['message']}")
    
    if not conn_result['success']:
        print("连接失败，程序退出")
        return
    
    # 2. 查询单页数据
    print("\n2. 查询2023年第1页数据 (每页5条)...")
    result = api.get_account_sum(year=2023, page=1, rows=5)
    
    if result['success']:
        data = result['data']
        records = data.get('accountsum', [])
        
        print(f"   总记录数: {data.get('row_count', 0)}")
        print(f"   总页数: {data.get('page_count', 0)}")
        print(f"   返回记录: {len(records)}条")
        
        if records:
            print("\n   数据示例:")
            for i, record in enumerate(records[:3], 1):
                print(f"     {i}. {record.get('ccode')} - {record.get('ccode_name')} "
                      f"(余额: {record.get('me', '0')})")
    else:
        print(f"   查询失败: {result['error']}")
    
    # 3. 查询多页数据
    print("\n3. 查询多页数据 (最多3页)...")
    all_result = api.get_all_pages(year=2023, max_pages=3, rows=10)
    
    if all_result['success']:
        print(f"   获取页数: {all_result['pages_retrieved']}")
        print(f"   总记录数: {all_result['total_records']}")
        
        # 保存数据到文件
        with open('account_data_basic.json', 'w', encoding='utf-8') as f:
            json.dump(all_result['data'], f, ensure_ascii=False, indent=2)
        print("   数据已保存到: account_data_basic.json")
    else:
        print(f"   查询失败: {all_result['error']}")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    main()

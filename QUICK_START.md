# 快速开始指南

## 🚀 5分钟快速体验

### 第一步：安装依赖
```bash
pip install -r requirements.txt
```

### 第二步：启动Web应用
```bash
python report_web.py
```

### 第三步：访问系统
打开浏览器访问：`http://localhost:5002`

### 第四步：生成报表
1. 点击"快速演示"按钮
2. 等待数据加载和报表生成
3. 在"报表文件"区域查看生成的文件
4. 点击HTML文件的预览按钮查看图表

## 📊 生成完整报表

### 命令行方式（推荐用于批量处理）
```bash
# 生成2023年完整报表
python generate_reports.py 2023

# 快速演示（少量数据）
python generate_reports.py demo
```

### Web界面方式（推荐用于日常使用）
1. 启动Web应用：`python report_web.py`
2. 访问：`http://localhost:5002`
3. 设置参数：
   - 年度：2023
   - 最大页数：10（获取更多数据）
4. 点击"生成所有报表"
5. 等待完成后下载查看

## 📁 输出文件说明

生成的报表文件保存在 `reports/` 目录下：

### Excel报表（.xlsx）
- **汇总报表_时间戳.xlsx**
  - 工作表1：按科目汇总
  - 工作表2：按期间汇总  
  - 工作表3：原始数据

### 交互式图表（.html）
- **余额趋势图_时间戳.html** - 各期间余额变化
- **科目分布图_时间戳.html** - 科目余额分布饼图
- **借贷对比图_时间戳.html** - 借贷发生额对比
- **综合仪表板_时间戳.html** - 多图表综合视图

## 🔧 常用操作

### 查看系统状态
Web界面会显示：
- Token状态和剩余时间
- API连接状态
- 请求频率统计

### 下载报表文件
1. 在Web界面的"报表文件"区域
2. 点击文件旁的下载按钮
3. 文件会自动下载到浏览器默认下载目录

### 在线预览图表
1. 点击HTML文件旁的预览按钮
2. 在新标签页中查看交互式图表
3. 支持缩放、悬停查看详情等操作

## ⚠️ 注意事项

1. **首次使用**：系统会自动获取Token，请确保网络连接正常
2. **数据量控制**：建议首次使用时设置较小的页数（如5页）
3. **端口占用**：确保5002端口未被其他程序占用
4. **文件权限**：确保程序有权限在当前目录创建 `reports/` 文件夹

## 🆘 遇到问题？

### 常见解决方案
1. **依赖安装失败**：使用 `pip install --upgrade pip` 更新pip后重试
2. **Web应用启动失败**：检查端口5002是否被占用
3. **Token获取失败**：检查网络连接和防火墙设置
4. **报表生成失败**：查看控制台错误信息，通常是网络或权限问题

### 获取帮助
查看完整文档：`README.md`

"""
API扩展示例 - 展示如何基于现有架构添加更多接口
"""

from token_manager import TokenManager
import requests
from datetime import datetime


class ExtendedYonyouAPI:
    """扩展的用友API客户端 - 支持多个接口"""
    
    def __init__(self):
        self.token_manager = TokenManager()
        self.base_url = "https://api.yonyouup.com"
        self.from_account = "hcopenapi"
        self.app_key = "opad51476ef9c6cffec"
    
    def _make_request(self, endpoint, params=None):
        """通用请求方法"""
        token = self.token_manager.get_token()
        if not token:
            return {'success': False, 'error': '无法获取token'}
        
        # 添加通用参数
        request_params = {
            'from_account': self.from_account,
            'to_account': self.from_account,
            'app_key': self.app_key,
            'token': token
        }
        
        if params:
            request_params.update(params)
        
        try:
            response = requests.get(f"{self.base_url}{endpoint}", params=request_params, timeout=30)
            if response.status_code == 200:
                return {'success': True, 'data': response.json()}
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    # 1. 账户汇总接口 (已有)
    def get_account_sum(self, year=2023, page=1, rows=20):
        """账户汇总批量获取"""
        params = {
            'page_index': page,
            'rows_per_page': rows,
            'iyear': year
        }
        return self._make_request('/api/accountsum/batch_get', params)
    
    # 2. 科目余额接口 (示例扩展)
    def get_subject_balance(self, year=2023, period=12):
        """科目余额查询 (假设的接口)"""
        params = {
            'iyear': year,
            'iperiod': period
        }
        return self._make_request('/api/subjectbalance/get', params)
    
    # 3. 凭证查询接口 (示例扩展)
    def get_vouchers(self, year=2023, month=12, page=1, rows=20):
        """凭证查询 (假设的接口)"""
        params = {
            'iyear': year,
            'imonth': month,
            'page_index': page,
            'rows_per_page': rows
        }
        return self._make_request('/api/voucher/batch_get', params)
    
    # 4. 客户信息接口 (示例扩展)
    def get_customers(self, page=1, rows=50):
        """客户信息查询 (假设的接口)"""
        params = {
            'page_index': page,
            'rows_per_page': rows
        }
        return self._make_request('/api/customer/batch_get', params)
    
    # 5. 供应商信息接口 (示例扩展)
    def get_suppliers(self, page=1, rows=50):
        """供应商信息查询 (假设的接口)"""
        params = {
            'page_index': page,
            'rows_per_page': rows
        }
        return self._make_request('/api/supplier/batch_get', params)


# 使用示例
def demo_extended_api():
    """演示扩展API的使用"""
    print("=== 扩展API使用示例 ===\n")
    
    api = ExtendedYonyouAPI()
    
    # 1. 账户汇总 (已验证可用)
    print("1. 获取账户汇总...")
    result = api.get_account_sum(2023, 1, 5)
    if result['success']:
        data = result['data']
        print(f"   成功: 获取到 {len(data.get('accountsum', []))} 条记录")
    else:
        print(f"   失败: {result['error']}")
    
    # 2. 其他接口 (需要根据实际API文档调整)
    print("\n2. 其他接口示例 (需要实际API支持):")
    
    interfaces = [
        ("科目余额", lambda: api.get_subject_balance(2023, 12)),
        ("凭证查询", lambda: api.get_vouchers(2023, 12, 1, 10)),
        ("客户信息", lambda: api.get_customers(1, 10)),
        ("供应商信息", lambda: api.get_suppliers(1, 10))
    ]
    
    for name, func in interfaces:
        print(f"   {name}: 接口已封装，等待实际API文档确认")


if __name__ == "__main__":
    demo_extended_api()

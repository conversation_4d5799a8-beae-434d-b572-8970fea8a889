"""
Token管理器 - 负责获取、缓存和管理API访问token
"""

import requests
import time
import threading
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from dataclasses import dataclass

from config import API_CONFIG, TOKEN_CONFIG
from utils import setup_logger, is_token_expired, get_remaining_time, format_time_remaining


@dataclass
class TokenInfo:
    """Token信息数据类"""
    token: str
    obtained_at: datetime
    expires_at: datetime


class TokenManager:
    """
    Token管理器类
    
    功能：
    - 自动获取和缓存token
    - 检查token有效性
    - 控制请求频率
    - 线程安全操作
    """
    
    def __init__(self):
        self.logger = setup_logger('TokenManager')
        self._lock = threading.Lock()
        self._current_token: Optional[TokenInfo] = None
        self._request_history = []  # 存储请求时间历史
        
        self.logger.info("TokenManager初始化完成")
    
    def _clean_request_history(self) -> None:
        """清理过期的请求历史记录"""
        cutoff_time = datetime.now() - timedelta(hours=TOKEN_CONFIG['period_hours'])
        self._request_history = [
            req_time for req_time in self._request_history 
            if req_time > cutoff_time
        ]
    
    def _can_make_request(self) -> bool:
        """检查是否可以发起新的token请求"""
        self._clean_request_history()
        return len(self._request_history) < TOKEN_CONFIG['max_requests_per_period']
    
    def _record_request(self) -> None:
        """记录新的请求时间"""
        self._request_history.append(datetime.now())
    
    def _fetch_token_from_api(self) -> Optional[str]:
        """
        从API获取新的token

        Returns:
            获取到的token字符串，失败时返回None
        """
        if not self._can_make_request():
            self.logger.warning(
                f"请求频率限制：2小时内已请求{len(self._request_history)}次，"
                f"最大限制{TOKEN_CONFIG['max_requests_per_period']}次"
            )
            return None

        params = {
            'from_account': API_CONFIG['from_account'],
            'app_key': API_CONFIG['app_key'],
            'app_secret': API_CONFIG['app_secret']
        }

        for attempt in range(TOKEN_CONFIG['retry_attempts']):
            try:
                self.logger.info(f"正在获取token（第{attempt + 1}次尝试）...")

                response = requests.get(
                    API_CONFIG['base_url'],
                    params=params,
                    timeout=TOKEN_CONFIG['request_timeout']
                )

                if response.status_code == 200:
                    # 记录成功的请求
                    self._record_request()

                    # 解析JSON响应
                    try:
                        result = response.json()

                        # 检查API响应状态
                        if result.get('errcode') == '0':
                            token_data = result.get('token', {})
                            token_id = token_data.get('id')

                            if token_id:
                                self.logger.info(f"成功获取token: {token_id[:20]}...")
                                return token_id
                            else:
                                self.logger.error("响应中未找到token ID")
                        else:
                            self.logger.error(f"API返回错误: {result.get('errmsg', '未知错误')}")

                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSON解析失败: {str(e)}")
                        self.logger.error(f"响应内容: {response.text}")

                else:
                    self.logger.error(f"API请求失败，状态码: {response.status_code}")
                    self.logger.error(f"响应内容: {response.text}")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常（第{attempt + 1}次尝试）: {str(e)}")

                if attempt < TOKEN_CONFIG['retry_attempts'] - 1:
                    time.sleep(TOKEN_CONFIG['retry_delay'])

        self.logger.error("获取token失败，已达到最大重试次数")
        return None
    
    def get_token(self, force_refresh: bool = False) -> Optional[str]:
        """
        获取有效的token
        
        Args:
            force_refresh: 是否强制刷新token
            
        Returns:
            有效的token字符串，失败时返回None
        """
        with self._lock:
            # 检查当前token是否有效
            if (not force_refresh and 
                self._current_token and 
                not is_token_expired(self._current_token.obtained_at, TOKEN_CONFIG['lifetime_hours'])):
                
                remaining = get_remaining_time(
                    self._current_token.obtained_at, 
                    TOKEN_CONFIG['lifetime_hours']
                )
                if remaining:
                    self.logger.debug(
                        f"使用缓存的token，剩余时间: {format_time_remaining(remaining)}"
                    )
                    return self._current_token.token
            
            # 获取新token
            new_token = self._fetch_token_from_api()
            if new_token:
                now = datetime.now()
                self._current_token = TokenInfo(
                    token=new_token,
                    obtained_at=now,
                    expires_at=now + timedelta(hours=TOKEN_CONFIG['lifetime_hours'])
                )
                self.logger.info(f"新token已缓存，过期时间: {self._current_token.expires_at}")
                return new_token
            
            return None
    
    def get_token_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前token的详细信息
        
        Returns:
            包含token信息的字典，如果没有有效token则返回None
        """
        with self._lock:
            if not self._current_token:
                return None
            
            remaining = get_remaining_time(
                self._current_token.obtained_at,
                TOKEN_CONFIG['lifetime_hours']
            )
            
            return {
                'token': self._current_token.token[:20] + '...',  # 只显示前20个字符
                'obtained_at': self._current_token.obtained_at.isoformat(),
                'expires_at': self._current_token.expires_at.isoformat(),
                'is_expired': remaining is None,
                'remaining_time': format_time_remaining(remaining) if remaining else '已过期',
                'requests_in_period': len(self._request_history),
                'max_requests_per_period': TOKEN_CONFIG['max_requests_per_period']
            }
    
    def clear_cache(self) -> None:
        """清除缓存的token"""
        with self._lock:
            self._current_token = None
            self.logger.info("Token缓存已清除")

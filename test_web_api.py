"""
测试Web API接口
"""

import requests
import json
import time


def test_web_api():
    """测试Web API的各个端点"""
    
    base_url = "http://localhost:5000"
    
    print("=== Web API测试 ===\n")
    
    # 1. 测试token信息接口
    print("1. 测试Token信息接口...")
    try:
        response = requests.get(f"{base_url}/api/token_info")
        result = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   成功: {result.get('success', False)}")
        if result.get('success'):
            print(f"   Token信息: {result['data']['token']}")
        else:
            print(f"   消息: {result.get('message', '无消息')}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    print()
    
    # 2. 测试连接测试接口
    print("2. 测试连接测试接口...")
    try:
        response = requests.get(f"{base_url}/api/test_connection")
        result = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   成功: {result.get('success', False)}")
        print(f"   消息: {result.get('message', '无消息')}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    print()
    
    # 3. 测试账户汇总查询接口
    print("3. 测试账户汇总查询接口...")
    try:
        params = {
            'iyear': 2023,
            'page_index': 1,
            'rows_per_page': 3
        }
        response = requests.get(f"{base_url}/api/account_sum", params=params)
        result = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   成功: {result.get('success', False)}")
        
        if result.get('success'):
            data = result['data']
            if 'accountsum' in data:
                records = data['accountsum']
                print(f"   返回记录数: {len(records)}")
                print(f"   总记录数: {data.get('row_count', '未知')}")
                print(f"   总页数: {data.get('page_count', '未知')}")
                
                if records:
                    print(f"   第一条记录: {records[0]['ccode']} - {records[0]['ccode_name']}")
            else:
                print(f"   数据格式: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        else:
            print(f"   错误: {result.get('error', '未知错误')}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    print()
    
    # 4. 测试刷新token接口
    print("4. 测试刷新Token接口...")
    try:
        response = requests.post(f"{base_url}/api/refresh_token")
        result = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   成功: {result.get('success', False)}")
        print(f"   消息: {result.get('message', '无消息')}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    # 等待一下确保Web服务器完全启动
    print("等待Web服务器启动...")
    time.sleep(2)
    
    test_web_api()

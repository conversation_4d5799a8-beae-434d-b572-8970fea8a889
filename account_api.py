"""
账户汇总API接口封装
"""

import requests
from typing import Optional, Dict, Any, List
from datetime import datetime
from token_manager import TokenManager
from config import API_CONFIG
from utils import setup_logger


class AccountSumAPI:
    """
    账户汇总API类
    
    功能：
    - 封装账户汇总批量获取接口
    - 自动处理token管理
    - 支持分页查询
    - 数据格式化和验证
    """
    
    def __init__(self):
        """初始化账户汇总API"""
        self.token_manager = TokenManager()
        self.logger = setup_logger('AccountSumAPI')
        self.base_url = "https://api.yonyouup.com/api/accountsum"
        
        # 固定参数
        self.fixed_params = {
            'from_account': API_CONFIG['from_account'],
            'to_account': API_CONFIG['from_account'],  # 使用相同的账户
            'app_key': API_CONFIG['app_key']
        }
        
        self.logger.info("账户汇总API初始化完成")
    
    def _build_params(self, iyear: int, page_index: int = 1, 
                     rows_per_page: int = 20) -> Dict[str, Any]:
        """
        构建请求参数
        
        Args:
            iyear: 年度
            page_index: 页码（从1开始）
            rows_per_page: 每页行数
            
        Returns:
            完整的请求参数字典
        """
        # 获取token
        token = self.token_manager.get_token()
        if not token:
            raise Exception("无法获取有效的token")
        
        params = self.fixed_params.copy()
        params.update({
            'token': token,
            'page_index': page_index,
            'rows_per_page': rows_per_page,
            'iyear': iyear
        })
        
        return params
    
    def get_account_sum_batch(self, iyear: int, page_index: int = 1, 
                             rows_per_page: int = 20) -> Dict[str, Any]:
        """
        获取账户汇总批量数据
        
        Args:
            iyear: 年度
            page_index: 页码（从1开始）
            rows_per_page: 每页行数（建议不超过100）
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 当API调用失败时
        """
        try:
            # 构建参数
            params = self._build_params(iyear, page_index, rows_per_page)
            
            # 记录请求信息
            self.logger.info(f"请求账户汇总数据 - 年度: {iyear}, 页码: {page_index}, 每页: {rows_per_page}")
            
            # 发送请求
            response = requests.get(
                f"{self.base_url}/batch_get",
                params=params,
                timeout=30
            )
            
            # 检查响应状态
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.logger.info(f"成功获取数据，状态码: {response.status_code}")
                    return {
                        'success': True,
                        'data': data,
                        'request_params': {
                            'iyear': iyear,
                            'page_index': page_index,
                            'rows_per_page': rows_per_page
                        },
                        'timestamp': datetime.now().isoformat()
                    }
                except ValueError as e:
                    self.logger.error(f"JSON解析失败: {str(e)}")
                    return {
                        'success': False,
                        'error': f"响应数据格式错误: {str(e)}",
                        'raw_response': response.text,
                        'timestamp': datetime.now().isoformat()
                    }
            else:
                self.logger.error(f"API请求失败，状态码: {response.status_code}")
                return {
                    'success': False,
                    'error': f"API请求失败，状态码: {response.status_code}",
                    'raw_response': response.text,
                    'timestamp': datetime.now().isoformat()
                }
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求异常: {str(e)}")
            return {
                'success': False,
                'error': f"网络请求异常: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"未知异常: {str(e)}")
            return {
                'success': False,
                'error': f"未知异常: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
    
    def get_all_pages(self, iyear: int, max_pages: int = 10, 
                     rows_per_page: int = 20) -> Dict[str, Any]:
        """
        获取所有页面的数据
        
        Args:
            iyear: 年度
            max_pages: 最大页数限制
            rows_per_page: 每页行数
            
        Returns:
            包含所有页面数据的结果
        """
        all_data = []
        total_records = 0
        page_index = 1
        
        self.logger.info(f"开始获取所有页面数据 - 年度: {iyear}")
        
        while page_index <= max_pages:
            result = self.get_account_sum_batch(iyear, page_index, rows_per_page)
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f"第{page_index}页获取失败: {result['error']}",
                    'partial_data': all_data,
                    'pages_retrieved': page_index - 1,
                    'timestamp': datetime.now().isoformat()
                }
            
            # 解析数据
            page_data = result['data']
            if isinstance(page_data, dict) and 'accountsum' in page_data:
                records = page_data['accountsum']
                if records:
                    all_data.extend(records)
                    total_records += len(records)
                    self.logger.info(f"第{page_index}页获取成功，记录数: {len(records)}")

                    # 如果返回的记录数少于每页行数，说明已经是最后一页
                    if len(records) < rows_per_page:
                        break
                else:
                    # 没有更多数据
                    break
            else:
                # 数据格式不符合预期
                break
            
            page_index += 1
        
        return {
            'success': True,
            'data': all_data,
            'total_records': total_records,
            'pages_retrieved': page_index - 1,
            'request_params': {
                'iyear': iyear,
                'max_pages': max_pages,
                'rows_per_page': rows_per_page
            },
            'timestamp': datetime.now().isoformat()
        }
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试API连接
        
        Returns:
            连接测试结果
        """
        try:
            # 使用当前年度进行测试
            current_year = datetime.now().year
            result = self.get_account_sum_batch(current_year, 1, 1)
            
            return {
                'success': result['success'],
                'message': "连接测试成功" if result['success'] else f"连接测试失败: {result.get('error', '未知错误')}",
                'test_params': {'iyear': current_year, 'page_index': 1, 'rows_per_page': 1},
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f"连接测试异常: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
